import express from "express";
import {
  createTemplate,
  getTemplate,
  deleteTemplate,
  updateTemplate,
  updateTemplateStatus,
  createTemplateFrom<PERSON>son,
  getAllTemplate,
  createTemplateAnswer,
  getTemplateAnswer,
  deleteTemplateAnswer,
  updateTemplateAnswer,
  getAllTemplateAnswer,
  createLink,
  modifyQuestion,
  updateTemplateAnswerStatus,
} from "#controllers/template.controller.js";

/**
 * @swagger
 * components:
 *   schemas:
 *     Template:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Template unique identifier
 *         tem_name:
 *           type: string
 *           description: Template name
 *         tem_desc:
 *           type: string
 *           description: Template description
 *         status:
 *           type: string
 *           enum: [active, inactive]
 *           description: Template status
 *     TemplateAnswer:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Answer unique identifier
 *         template_id:
 *           type: string
 *           description: Reference to template
 *         answers:
 *           type: object
 *           description: Answer content
 *         status:
 *           type: string
 *           enum: [active, inactive]
 *           description: Answer status
 *     PaginationRequest:
 *       type: object
 *       properties:
 *         page:
 *           type: number
 *           description: Page number
 *         size:
 *           type: number
 *           description: Items per page
 *         keyword:
 *           type: string
 *           description: Search keyword
 */

const templateRoutes = express.Router();

/**
 * @swagger
 * /private/template/createTemplate:
 *   post:
 *     summary: Create a new template
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [tem_name]
 *             properties:
 *               tem_name:
 *                 type: string
 *               tem_desc:
 *                 type: string
 *     responses:
 *       200:
 *         description: Template created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 template:
 *                   $ref: '#/components/schemas/Template'
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/createTemplate").post(createTemplate);

/**
 * @swagger
 * /private/template/getTemplate:
 *   post:
 *     summary: Get template by ID
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id]
 *             properties:
 *               id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Template fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 template:
 *                   $ref: '#/components/schemas/Template'
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/getTemplate").post(getTemplate);

/**
 * @swagger
 * /private/template/deleteTemplate:
 *   post:
 *     summary: Delete a template
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id]
 *             properties:
 *               id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Template deleted successfully
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/deleteTemplate").post(deleteTemplate);

/**
 * @swagger
 * /private/template/updateTemplate:
 *   post:
 *     summary: Update template details
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id]
 *             properties:
 *               id:
 *                 type: string
 *               tem_name:
 *                 type: string
 *               tem_desc:
 *                 type: string
 *     responses:
 *       200:
 *         description: Template updated successfully
 *       400:
 *         description: Invalid request or template not found
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/updateTemplate").post(updateTemplate);

/**
 * @swagger
 * /private/template/updateTemplateStatus:
 *   post:
 *     summary: Update template status
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id, status]
 *             properties:
 *               id:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *     responses:
 *       200:
 *         description: Template status updated successfully
 *       400:
 *         description: Invalid request or template not found
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/updateTemplateStatus").post(updateTemplateStatus);

/**
 * @swagger
 * /private/template/createTemplateFromJson:
 *   post:
 *     summary: Create a template from JSON data
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               template:
 *                 $ref: '#/components/schemas/Template'
 *     responses:
 *       200:
 *         description: Template created successfully
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/createTemplateFromJson").post(createTemplateFromJson);

/**
 * @swagger
 * /private/template/getAllTemplate:
 *   post:
 *     summary: Get all templates with pagination
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PaginationRequest'
 *     responses:
 *       200:
 *         description: Templates fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 template:
 *                   type: object
 *                   properties:
 *                     rows:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Template'
 *                     count:
 *                       type: number
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/getAllTemplate").post(getAllTemplate);

/**
 * @swagger
 * /private/template/createTemplateAnswer:
 *   post:
 *     summary: Create a template answer
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TemplateAnswer'
 *     responses:
 *       200:
 *         description: Template answer created successfully
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/createTemplateAnswer").post(createTemplateAnswer);

/**
 * @swagger
 * /private/template/getTemplateAnswer:
 *   post:
 *     summary: Get template answer by ID
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id]
 *             properties:
 *               id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Template answer fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 template:
 *                   $ref: '#/components/schemas/TemplateAnswer'
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/getTemplateAnswer").post(getTemplateAnswer);

/**
 * @swagger
 * /private/template/deleteTemplateAnswer:
 *   post:
 *     summary: Delete a template answer
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id]
 *             properties:
 *               id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Template answer deleted successfully
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/deleteTemplateAnswer").post(deleteTemplateAnswer);

/**
 * @swagger
 * /private/template/updateTemplateAnswer:
 *   post:
 *     summary: Update template answer
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id]
 *             properties:
 *               id:
 *                 type: string
 *               answers:
 *                 type: object
 *     responses:
 *       200:
 *         description: Template answer updated successfully
 *       400:
 *         description: Invalid request or answer not found
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/updateTemplateAnswer").post(updateTemplateAnswer);

/**
 * @swagger
 * /private/template/getAllTemplateAnswer:
 *   post:
 *     summary: Get all template answers with pagination
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             allOf:
 *               - $ref: '#/components/schemas/PaginationRequest'
 *               - type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     enum: [active, inactive]
 *     responses:
 *       200:
 *         description: Template answers fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 template:
 *                   type: object
 *                   properties:
 *                     rows:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/TemplateAnswer'
 *                     count:
 *                       type: number
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/getAllTemplateAnswer").post(getAllTemplateAnswer);

/**
 * @swagger
 * /private/template/updateTemplateAnswerStatus:
 *   post:
 *     summary: Update template answer status
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id, status]
 *             properties:
 *               id:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [active, inactive]
 *     responses:
 *       200:
 *         description: Template answer status updated successfully
 *       400:
 *         description: Invalid request or answer not found
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes
  .route("/updateTemplateAnswerStatus")
  .post(updateTemplateAnswerStatus);

/**
 * @swagger
 * /private/template/createLink:
 *   post:
 *     summary: Create a link for a template
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id]
 *             properties:
 *               id:
 *                 type: string
 *                 description: Template ID to create link for
 *     responses:
 *       200:
 *         description: Link created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 template:
 *                   type: object
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/createLink").post(createLink);

/**
 * @swagger
 * /private/template/modifyQuestion:
 *   post:
 *     summary: Modify a question in a template
 *     tags: [Template]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [id, question]
 *             properties:
 *               id:
 *                 type: string
 *                 description: Template ID
 *               question:
 *                 type: object
 *                 description: Modified question data
 *     responses:
 *       200:
 *         description: Question modified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 template:
 *                   type: object
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Permission denied or server error
 */
templateRoutes.route("/modifyQuestion").post(modifyQuestion);

// Add these routes to existing template routes
router.put("/:id/export-config", routeProtection, updateTemplateExportConfig);
router.get("/:id/export-config", routeProtection, getTemplateExportConfig);

export default templateRoutes;

